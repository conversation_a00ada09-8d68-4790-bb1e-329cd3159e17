upload Error : 
Page not found (404)
Request Method:	GET
Request URL:	http://127.0.0.1:8000//employee/bulk-update/upload/
Using the URLconf defined in horilla.urls, <PERSON><PERSON><PERSON> tried these URL patterns, in this order:

admin/
accounts/
accounts/
[name='home-page']
initialize-database [name='initialize-database']
load-demo-database [name='load-demo-database']
initialize-database-user [name='initialize-database-user']
initialize-database-company [name='initialize-database-company']
initialize-database-department [name='initialize-database-department']
initialize-department-edit/<int:obj_id> [name='initialize-department-edit']
initialize-department-delete/<int:obj_id> [name='initialize-department-delete']
initialize-database-job-position [name='initialize-database-job-position']
initialize-job-position-edit/<int:obj_id> [name='initialize-job-position-edit']
initialize-job-position-delete/<int:obj_id> [name='initialize-job-position-delete']
404 [name='404']
login/ [name='login']
forgot-password [name='forgot-password']
employee-reset-password [name='employee-reset-password']
reset-send-success [name='reset-send-success']
change-password [name='change-password']
change-username [name='change-username']
two-factor [name='two-factor']
send-otp [name='send-otp']
logout [name='logout']
settings [name='settings']
settings/user-group-create/ [name='user-group-create']
settings/user-group-view/ [name='user-group-view']
settings/user-group-search/ [name='user-group-search']
user-group-delete/<int:obj_id>/ [name='user-group-delete']
group-permission-remove/<int:pid>/<int:gid>/ [name='group-permission-remove']
user-group-assign-view [name='user-group-assign-view']
settings/user-group-assign/ [name='user-group-assign']
group-remove-user/<int:uid>/<int:gid>/ [name='group-remove-user']
settings/employee-permission-assign/ [name='employee-permission-assign']
employee-permission-search [name='permission-search']
update-user-permission [name='update-user-permission']
update-group-permission [name='update-group-permission']
permission-table [name='permission-table']
settings/mail-server-conf/ [name='mail-server-conf']
settings/mail-server-create-update/ [name='mail-server-create-update']
settings/mail-server-test-email/ [name='mail-server-test-email']
mail-server-delete [name='mail-server-delete']
replace-primary-mail [name='replace-primary-mail']
configuration/view-mail-templates/ [name='view-mail-templates']
view-mail-template/<int:obj_id>/ [name='view-mail-template']
create-mail-template/ [name='create-mail-template']
duplicate-mail-template/<int:obj_id>/ [name='duplicate-mail-template']
delete-mail-template/ [name='delete-mail-template']
settings/company-create/ [name='company-create']
settings/company-view/ [name='company-view']
settings/company-update/<int:id>/ [name='company-update']
settings/company-delete/<int:obj_id>/ [name='company-delete']
settings/department-view/ [name='department-view']
settings/department-creation/ [name='department-creation']
settings/department-update/<int:id>/ [name='department-update']
department-delete/<int:obj_id>/ [name='department-delete']
settings/job-position-creation/ [name='job-position-creation']
settings/job-position-view/ [name='job-position-view']
settings/job-position-update/<int:id>/ [name='job-position-update']
job-position-delete/<int:obj_id>/ [name='job-position-delete']
settings/job-role-create/ [name='job-role-create']
settings/job-role-view/ [name='job-role-view']
settings/job-role-update/<int:id>/ [name='job-role-update']
job-role-delete/<int:obj_id>/ [name='job-role-delete']
settings/work-type-view/ [name='work-type-view']
settings/work-type-create/ [name='work-type-create']
settings/work-type-update/<int:id>/ [name='work-type-update']
work-type-delete/<int:obj_id>/ [name='work-type-delete']
add-remove-work-type-fields [name='add-remove-work-type-fields']
settings/rotating-work-type-create/ [name='rotating-work-type-create']
settings/rotating-work-type-view/ [name='rotating-work-type-view']
settings/rotating-work-type-update/<int:id>/ [name='rotating-work-type-update']
rotating-work-type-delete/<int:obj_id>/ [name='rotating-work-type-delete']
employee/rotating-work-type-assign/ [name='rotating-work-type-assign']
rotating-work-type-assign-add [name='rotating-work-type-assign-add']
rotating-work-type-assign-view [name='rotating-work-type-assign-view']
rotating-work-type-assign-export [name='rotating-work-type-assign-export']
settings/rotating-work-type-assign-update/<int:id>/ [name='rotating-work-type-assign-update']
rotating-work-type-assign-duplicate/<int:obj_id>/ [name='rotating-work-type-assign-duplicate']
rotating-work-type-assign-archive/<int:obj_id>/ [name='rotating-work-type-assign-archive']
rotating-work-type-assign-bulk-archive [name='rotating-shift-work-type-bulk-archive']
rotating-work-type-assign-bulk-delete [name='rotating-shift-work-type-bulk-delete']
rotating-work-type-assign-delete/<int:obj_id>/ [name='rotating-work-type-assign-delete']
settings/employee-type-view/ [name='employee-type-view']
settings/employee-type-create/ [name='employee-type-create']
settings/employee-type-update/<int:id>/ [name='employee-type-update']
employee-type-delete/<int:obj_id>/ [name='employee-type-delete']
settings/employee-shift-view/ [name='employee-shift-view']
settings/employee-shift-create/ [name='employee-shift-create']
settings/employee-shift-update/<int:id>/ [name='employee-shift-update']
employee-shift-delete/<int:obj_id>/ [name='employee-shift-delete']
settings/employee-shift-schedule-view/ [name='employee-shift-schedule-view']
settings/employee-shift-schedule-create/ [name='employee-shift-schedule-create']
settings/employee-shift-schedule-update/<int:id>/ [name='employee-shift-schedule-update']
employee-shift-schedule-delete/<int:obj_id>/ [name='employee-shift-schedule-delete']
settings/rotating-shift-create/ [name='rotating-shift-create']
add-remove-shift-fields [name='add-remove-shift-fields']
settings/rotating-shift-view/ [name='rotating-shift-view']
settings/rotating-shift-update/<int:id>/ [name='rotating-shift-update']
rotating-shift-delete/<int:obj_id>/ [name='rotating-shift-delete']
employee/rotating-shift-assign/ [name='rotating-shift-assign']
rotating-shift-assign-add [name='rotating-shift-assign-add']
rotating-shift-assign-view [name='rotating-shift-assign-view']
rotating-shift-assign-info-export [name='rotating-shift-assign-info-export']
rotating-shift-assign-info-import [name='rotating-shift-assign-info-import']
settings/rotating-shift-assign-update/<int:id>/ [name='rotating-shift-assign-update']
rotating-shift-assign-duplicate/<int:obj_id>/ [name='rotating-shift-assign-duplicate']
rotating-shift-assign-archive/<int:obj_id>/ [name='rotating-shift-assign-archive']
rotating-shift-assign-bulk-archive [name='rotating-shift-assign-bulk-archive']
rotating-shift-assign-bulk-delete [name='rotating-shift-assign-bulk-delete']
rotating-shift-assign-delete/<int:obj_id>/ [name='rotating-shift-assign-delete']
work-type-request [name='work-type-request']
work-type-request-duplicate/<int:obj_id>/ [name='work-type-request-duplicate']
employee/work-type-request-view/ [name='work-type-request-view']
work-type-request-info-export [name='work-type-request-info-export']
work-type-request-search [name='work-type-request-search']
work-type-request-cancel/<int:id>/ [name='work-type-request-cancel']
work-type-request-bulk-cancel [name='work-type-request-bulk-cancel']
work-type-request-approve/<int:id>/ [name='work-type-request-approve']
work-type-request-bulk-approve [name='work-type-request-bulk-approve']
work-type-request-update/<int:work_type_request_id>/ [name='work-type-request-update']
work-type-request-delete/<int:obj_id>/ [name='work-type-request-delete']
work-type-request-single-view/<int:obj_id>/ [name='work-type-request-single-view']
work-type-request-bulk-delete [name='work-type-request-bulk-delete']
shift-request [name='shift-request']
shift-request-duplicate/<int:obj_id>/ [name='shift-request-duplicate']
shift-request-reallocate [name='shift-request-reallocate']
update-employee-allocation [name='update-employee-allocation']
employee/shift-request-view/ [name='shift-request-view']
shift-request-info-export [name='shift-request-info-export']
shift-request-search [name='shift-request-search']
shift-request-details/<int:id>/ [name='shift-request-details']
shift-allocation-request-details/<int:id>/ [name='shift-allocation-request-details']
shift-request-update/<int:shift_request_id>/ [name='shift-request-update']
shift-allocation-request-update/<int:shift_request_id>/ [name='shift-allocation-request-update']
shift-request-cancel/<int:id>/ [name='shift-request-cancel']
shift-allocation-request-cancel/<int:id>/ [name='shift-allocation-request-cancel']
shift-request-bulk-cancel [name='shift-request-bulk-cancel']
shift-request-approve/<int:id>/ [name='shift-request-approve']
shift-allocation-request-approve/<int:id>/ [name='shift-allocation-request-approve']
shift-request-bulk-approve [name='shift-request-bulk-approve']
shift-request-delete/<int:id>/ [name='shift-request-delete']
shift-request-bulk-delete [name='shift-request-bulk-delete']
notifications [name='notifications']
clear-notifications [name='clear-notifications']
delete-all-notifications [name='delete-all-notifications']
read-notifications [name='read-notifications']
mark-as-read-notification/<int:notification_id> [name='mark-as-read-notification']
mark-as-read-notification-json/ [name='mark-as-read-notification-json']
all-notifications [name='all-notifications']
delete-notifications/<id>/ [name='delete-notifications']
settings/general-settings/ [name='general-settings']
settings/date-settings/ [name='date-settings']
settings/save-date/ [name='save_date_format']
settings/get-date-format/ [name='get-date-format']
settings/save-time/ [name='save_time_format']
settings/get-time-format/ [name='get-time-format']
history-field-settings [name='history-field-settings']
enable-account-block-unblock [name='enable-account-block-unblock']
enable-profile-edit-feature [name='enable-profile-edit-feature']
rwork-individual-view/<int:instance_id>/ [name='rwork-individual-view']
rshit-individual-view/<int:instance_id>/ [name='rshift-individual-view']
shift-select/ [name='shift-select']
shift-select-filter/ [name='shift-select-filter']
work-type-select/ [name='work-type-select']
work-type-filter/ [name='work-type-select-filter']
r-shift-select/ [name='r-shift-select']
r-shift-select-filter/ [name='r-shift-select-filter']
r-work-type-select/ [name='r-work-type-select']
r-work-type-filter/ [name='r-work-type-select-filter']
settings/tag-view/ [name='tag-view']
settings/helpdesk-tag-view/ [name='helpdesk-tag-view']
tag-create [name='tag-create']
tag-update/<int:tag_id> [name='tag-update']
tag-delete/<int:obj_id> [name='tag-delete']
audit-tag-create [name='audit-tag-create']
audit-tag-update/<int:tag_id> [name='audit-tag-update']
audit-tag-delete/<int:obj_id> [name='audit-tag-delete']
configuration/multiple-approval-condition [name='multiple-approval-condition']
configuration/condition-value-fields [name='condition-value-fields']
configuration/add-more-approval-managers [name='add-more-approval-managers']
configuration/remove-approval-manager [name='remove-approval-manager']
configuration/hx-multiple-approval-condition [name='hx-multiple-approval-condition']
multiple-level-approval-create [name='multiple-level-approval-create']
multiple-level-approval-edit/<int:condition_id> [name='multiple-level-approval-edit']
multiple-level-approval-delete/<int:condition_id> [name='multiple-level-approval-delete']
shift-request-add-comment/<int:shift_id>/ [name='shift-request-add-comment']
view-shift-comment/<int:shift_id>/ [name='view-shift-comment']
delete-shift-comment-file/ [name='delete-shift-comment-file']
view-work-type-comment/<int:work_type_id>/ [name='view-work-type-comment']
delete-work-type-comment-file/ [name='delete-work-type-comment-file']
shift-request-delete-comment/<int:comment_id>/ [name='shift-request-delete-comment']
worktype-request-add-comment/<int:worktype_id>/ [name='worktype-request-add-comment']
worktype-request-delete-comment/<int:comment_id>/ [name='worktype-request-delete-comment']
dashboard-shift-request [name='dashboard-shift-request']
dashboard-work-type-request [name='dashboard-work-type-request']
settings/pagination-settings-view/ [name='pagination-settings-view']
settings/action-type/ [name='action-type']
action-type-create [name='action-type-create']
action-type-update/<int:act_id> [name='action-type-update']
action-type-delete/<int:act_id> [name='action-type-delete']
pagination-settings-view [name='pagination-settings-view']
announcement-list [name='announcement-list']
create-announcement [name='create-announcement']
delete-announcement/<int:anoun_id> [name='delete-announcement']
update-announcement/<int:anoun_id> [name='update-announcement']
remove-announcement-file/<int:obj_id>/<int:attachment_id> [name='remove-announcement-file']
announcement-add-comment/<int:anoun_id>/ [name='announcement-add-comment']
announcement-view-comment/<int:anoun_id>/ [name='announcement-view-comment']
announcement-single-view/<int:anoun_id> [name='announcement-single-view']
announcement-single-view/ [name='announcement-single-view']
announcement-delete-comment/<int:comment_id>/ [name='announcement-delete-comment']
announcement-viewed-by [name='announcement-viewed-by']
driver-viewed [name='driver-viewed']
dashboard-components-toggle [name='dashboard-components-toggle']
employee-chart-show [name='employee-chart-show']
settings/enable-biometric-attendance/ [name='enable-biometric-attendance']
settings/activate-biometric-attendance [name='activate-biometric-attendance']
emp-workinfo-complete [name='emp-workinfo-complete']
get-horilla-installed-apps/ [name='get-horilla-installed-apps']
configuration/holiday-view [name='holiday-view']
configuration/holidays-excel-template [name='holidays-excel-template']
holidays-info-import [name='holidays-info-import']
holiday-info-export [name='holiday-info-export']
get-upcoming-holidays [name='get-upcoming-holidays']
holiday-creation [name='holiday-creation']
holiday-update/<int:obj_id> [name='holiday-update']
duplicate-holiday/<int:obj_id> [name='duplicate-holiday']
holiday-delete/<int:obj_id> [name='holiday-delete']
holidays-bulk-delete [name='holidays-bulk-delete']
holiday-filter [name='holiday-filter']
holiday-select/ [name='holiday-select']
holiday-select-filter/ [name='holiday-select-filter']
company-leave-creation [name='company-leave-creation']
configuration/company-leave-view [name='company-leave-view']
company-leave-update/<int:id> [name='company-leave-update']
company-leave-delete/<int:id> [name='company-leave-delete']
company-leave-filter [name='company-leave-filter']
view-penalties [name='view-penalties']
^media/(?P<path>.*)$ [name='protected_media']
update-selected-company [name='update-selected-company']
configuration/mail-automations [name='mail-automations']
mail-automations-nav [name='mail-automations-nav']
create-automation [name='create-automation']
update-automation/<int:pk>/ [name='update-automation']
mail-automations-list-view [name='mail-automations-list-view']
get-to-mail-field [name='get-to-mail-field']
automation-detailed-view/<int:pk>/ [name='automation-detailed-view']
delete-automation/<int:pk>/ [name='delete-automation']
load-automations [name='load-automations']
refresh-automations [name='refresh-automations']
toggle-columns [name='toggle-columns']
active-tab [name='active-tab']
active-group [name='cbv-active-group']
reload-field [name='reload-field']
reload-messages [name='reload-messages']
saved-filter/ [name='saved-filter']
saved-filter/<int:pk>/ [name='saved-filter-update']
delete-saved-filter/<int:pk>/ [name='delete-saved-filter']
active-hnv-view-type/ [name='active-hnv-view-type']
search-in-instance-ids [name='search-in-instance-ids']
last-applied-filter [name='last-applied-filter']
generic-delete [name='generic-delete']
horilla-history-revert/<int:pk>/<int:history_id>/ [name='history-revert']
employee/
horilla-widget/
^inbox/notifications/
i18n/
health/
recruitment/
leave/
pms/
onboarding/
asset/
attendance/
payroll/
user-accessibility/ [name='user-accessibility']
get-initial-accessibility-data [name='get-initial-accessibility-data']
biometric/
helpdesk/
offboarding/
backup/
project/
api/
api/geofencing/
api/facedetection/
horilla-audit-log [name='horilla-audit-log']
recruitment/
onboarding/
employee/
attendance/
leave/
payroll/
pms/
asset/
project/
The current path, /employee/bulk-update/upload/, didn’t match any of these.

You’re seeing this error because you have DEBUG = True in your Django settings file. Change that to False, and Django will display a standard 404 page.